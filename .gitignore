# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/

# Environment variables
.env

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Uploads
uploads/

# Redis
*.rdb

# OS specific
.DS_Store
Thumbs.db

projectDetails.txt

# Docs
project_docs/


AnyDocAI_Frontend_Phase1.txt